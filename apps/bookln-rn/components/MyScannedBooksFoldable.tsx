import {
  JglStateView,
  JglText,
  JglTouchable,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { FlashList } from '@shopify/flash-list';
import { ChevronDown } from '@tamagui/lucide-icons';
import { useMount } from 'ahooks';
import { useCallback, useState } from 'react';
import { StyleSheet } from 'react-native';
import Animated, {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { Shadow } from 'react-native-shadow-2';
import { twMerge } from 'tailwind-merge';
import type { BookDTO } from '../api/dto';
import BizConfig from '../constants/BizConfig';
import { useMineContentScannedBooks } from '../hooks/useMineContentScannedBooks';
import { BookItem } from './BookItem';

type Props = {
  /**
   * 是否显示空状态
   */
  isShowEmpty?: boolean;
  /** 初始化组件时是否展开（默认true） */
  firstExpanded?: boolean;
};

/**
 * @description 我扫码看过的书 - 折叠组件
 * <AUTHOR>
 * @date 2025-06-15
 */
export const MyScannedBooksFoldable = (props: Props) => {
  const { isShowEmpty, firstExpanded = true } = props;

  const [expanded, setExpanded] = useState(firstExpanded);

  const { bookShelf, onPressMore, retry } = useMineContentScannedBooks();

  const animatedHeight = useSharedValue(firstExpanded ? 1 : 0);
  const animatedRotation = useSharedValue(firstExpanded ? 1 : 0);

  const toggleExpanded = useCallback(() => {
    setExpanded((prev) => {
      const newExpanded = !prev;
      animatedHeight.value = withTiming(newExpanded ? 1 : 0, { duration: 300 });
      animatedRotation.value = withTiming(newExpanded ? 1 : 0, {
        duration: 300,
      });
      return newExpanded;
    });
  }, [animatedHeight, animatedRotation]);

  const keyExtractor = useCallback((item: BookDTO) => `${item.id}`, []);

  const animatedContainerStyle = useAnimatedStyle(() => {
    // +2是为了让阴影边界更自然，不会截断
    const expandedHeight = 127 + 12 + 15 + 2;
    const height = interpolate(
      animatedHeight.value,
      [0, 1],
      [0, expandedHeight],
    );
    const opacity = interpolate(animatedHeight.value, [0, 1], [0, 1]);

    return {
      height: height,
      opacity: opacity,
      overflow: 'hidden',
    };
  });

  const animatedChevronStyle = useAnimatedStyle(() => {
    const rotation = interpolate(animatedRotation.value, [0, 1], [0, 180]);

    return {
      transform: [{ rotate: `${rotation}deg` }],
    };
  });

  const renderItem = useCallback(({ item }: { item: BookDTO }) => {
    return (
      <JglXStack jglClassName='w-[92px] aspect-[92/130]'>
        <BookItem book={item} maxDisplayLines={1} />
      </JglXStack>
    );
  }, []);

  useMount(() => {
    animatedHeight.value = expanded ? 1 : 0;
    animatedRotation.value = expanded ? 1 : 0;
  });

  if (!isShowEmpty && bookShelf.length === 0) {
    return null;
  }

  return (
    <JglYStack jglClassName='w-full items-center' py={16} space={12}>
      <JglXStack
        jglClassName='w-full items-center px-[16px]'
        justifyContent='space-between'
        alignItems='center'
      >
        <JglText fontSize={18} fontWeight='700' color='#151B37'>
          我扫码看过的书
        </JglText>

        <JglTouchable
          onPress={onPressMore}
          minH={0}
          display={
            bookShelf.length > BizConfig.MAX_DISPLAY_SCANNED_BOOKS
              ? 'flex'
              : 'none'
          }
        >
          <JglText fontSize={12} color='#9698A5'>
            查看更多
          </JglText>
        </JglTouchable>

        <JglTouchable onPress={toggleExpanded}>
          <JglText fontSize={12} color='#9698A5'>
            {expanded ? '收起' : '展开'}
          </JglText>
          <Animated.View style={animatedChevronStyle}>
            <ChevronDown color='#9698A5' size={16} />
          </Animated.View>
        </JglTouchable>
      </JglXStack>

      <Animated.View
        className={twMerge('w-full', expanded && 'min-h-[127px]')}
        style={animatedContainerStyle}
      >
        <JglStateView
          isEmpty={bookShelf.length === 0}
          onRetry={retry}
          backgroundColor='transparent'
          emptyProps={{
            message: '暂无图书',
          }}
        >
          <Shadow
            style={styles.expandShadow}
            paintInside={false}
            distance={15}
            startColor='#0000000d'
            sides={{ bottom: true, top: false, start: false, end: false }}
          >
            <JglXStack jglClassName='w-full pb-3'>
              <FlashList
                data={bookShelf.slice(0, BizConfig.MAX_DISPLAY_SCANNED_BOOKS)}
                renderItem={renderItem}
                contentContainerStyle={{
                  paddingHorizontal: 16,
                }}
                key={`${bookShelf.length}`}
                keyExtractor={keyExtractor}
                ItemSeparatorComponent={() => (
                  <JglXStack jglClassName='w-[12px]' />
                )}
                showsHorizontalScrollIndicator={false}
                horizontal
                showsVerticalScrollIndicator={false}
                alwaysBounceVertical={false}
              />
            </JglXStack>
          </Shadow>
        </JglStateView>
      </Animated.View>
    </JglYStack>
  );
};

const styles = StyleSheet.create({
  expandShadow: {
    width: '100%',
  },
});
